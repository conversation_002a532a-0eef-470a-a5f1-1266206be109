
#include "./client/asio_tcp_client.h"
#include "tools/tools.h"
using namespace std;
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <openssl/pkcs12.h>
#include <openssl/bio.h>
#include <openssl/err.h>
#include <openssl/pem.h>
#include <thread>
bool running = false;
#pragma pack(1)
typedef struct hsm_req_head{
     unsigned int tlen;                     //消息总长度
     unsigned int checksum;                 //消息体的校验和
     unsigned short action;                 //动作 = 函数的代号
     unsigned short version;                //主次版本号（0）前8位为主，后8位为次
     unsigned int reserve;                  //预留位
} HSM_REQ_HEAD;
#pragma pack()
void handle(cspSessionPool * pc,int max_send_buffer,int max_send_time){

printf("start handle\n");
    char *buffer = new char [max_send_buffer+100];
    char *recvbuffer = new char[max_send_buffer+100];
    char *sendbuffer = new char [max_send_buffer];
    memset(buffer,0,max_send_buffer+100);
    memset(sendbuffer,0,max_send_buffer);
    memset(recvbuffer,0,max_send_buffer+100);
    int len = 0;
    HSM_REQ_HEAD *header =(HSM_REQ_HEAD*)buffer ;
    memset(header,0,sizeof(HSM_REQ_HEAD));
    int glen=max_send_buffer+16;
    header->tlen = htonl(glen);
    for(int i=0;i<4;i++)
    {
        printf("%02x ",buffer[i]);
    }
    printf("\n");
    //return;
    header->action=0;
    memcpy(buffer+16,sendbuffer,sizeof(sendbuffer));
    buffer[16]=1;
    buffer[17]=2;
    buffer[18]=3;
    for(int i=0;i<max_send_time;i++)
    {
        cspSession *connection =  cspGetSessionFromPool(pc,200);
        if(!connection)
        {
            
            sleep(1);
            printf("connection is false\n");
             return;
            //continue;
        }
        //printf("send len:[%d]\n",glen);
        int ret  = cspSend(connection,buffer,glen,10);
        if(ret != 0 )
        {
            printf("write  false\n");
            cspBackSessionToPool(pc,connection);  
            return;
        }
        ret = cspRead(connection,recvbuffer,16,10000);
        if(ret <0)
        {
            printf("read1  false\n");
            cspBackSessionToPool(pc,connection);  
             return;;
        }
        //网络字节序转本地字节序   
        HSM_REQ_HEAD *h1 = ( HSM_REQ_HEAD *)recvbuffer;
       
        //printf("[%d]\n",h1->tlen);
        int bodylen  = ntohl(h1->tlen)-16;
        ret = cspRead(connection,recvbuffer+16, bodylen,10000);
        if(ret != bodylen)
        {
            printf("read2  false:[%d],[%d]\n",ret,bodylen);
            cspBackSessionToPool(pc,connection); 
            return;
        }
        cspBackSessionToPool(pc,connection);  

    }

}
 void show_message11(int lev, const char* str_message, ...)
{
    //使用va_list处理可变参数
    va_list args;
    va_start(args, str_message);
    vprintf(str_message, args);
    va_end(args);
    printf("\n");
}

void checkconnection(cspSessionPool * pc)
{
    while (true)
    {
        printf("1111\n");
        cspSession *connection =  cspGetSessionFromPool(pc,200);
        if (!connection)
        {
            printf("no connection ,now continue:[%d]\n",time(NULL));
            sleep(1);
            continue;
        }
        printf("2222\n");
    int ret = cspCheckConnection(connection);
    if(ret!=CSP_OK)
    {
        printf("connection is false\n");
        ret = cspReconnect(connection);
        if(ret!=CSP_OK)
        {
            printf("reconnect is false\n");
            cspBackSessionToPool(pc,connection);
            sleep(1);
            continue;
        }
        printf("reconnection is ok\n");
        cspBackSessionToPool(pc,connection);
        sleep(1);
        continue;
    }
    printf("connection is ok\n");
    char pdata[1024]={0};
    ret = cspRead(connection,pdata,5,1000);
    if(ret!=0)
    {
        printf("getdata is %s\n",pdata);
    }
   
    cspBackSessionToPool(pc,connection);
    sleep(1);
    }
}   
int main(int argc , char *argv[])
{
    cspOptions opt;
    memset(&opt,0,sizeof(cspOptions));
    
    memset(&opt.socks_options,0,sizeof(socksOptions_t));
    /*
    string str_dest = "172.20.111.203";
    int port = 9000;
    string str_proxy = "**************";
    int proxy_port = 1080;
    memcpy(opt.socks_options.dest_addr,str_dest.c_str(),str_dest.length());
    opt.socks_options.dest_port = port;
    memcpy(opt.socks_options.proxy_addr,str_proxy.c_str(),str_proxy.length());
    opt.socks_options.proxy_port = proxy_port;
    opt.socks_options.proxy_auth = 1;
    memcpy(opt.socks_options.proxy_user,"test1",5);
    memcpy(opt.socks_options.proxy_pass,"123455",6);
    opt.socks_options.proxy_conn_type = IP_V4_ADDR;
    opt.use_socks_proxy = 1;
    */
    running=true;
    opt.type=NO_TLS;
    opt.connect_timeout = 100000;
    string host="************";
    opt.server_port = 7898;
    opt.ssl_option.load_order=1;
    opt.connect_timeout=3000;
    opt.tcp_send_buffer=30720;
    opt.tcp_recv_buffer=30720;
    opt.ssl_option.verity_mode=0;
    opt.logger_ptr = show_message11;
     memcpy(opt.server_addr,host.c_str(),host.length());
     string enc12="/home/<USER>/newkey/sm2_sdkclient_enc_cert.pfx";
     string sign12="/home/<USER>/newkey/sm2_sdkclient_sign_cert.pfx";
     string passwd="12345678";
     string capath="/home/<USER>/newkey/RootCAforHSM_chain.cer";
     memcpy(opt.ssl_option.cafile,capath.c_str(),capath.length());
     memcpy(opt.ssl_option.encp12,enc12.c_str(),enc12.length());
     memcpy(opt.ssl_option.signp12,sign12.c_str(),sign12.length());
    memcpy(opt.ssl_option.encpasswd,passwd.c_str(),passwd.length());
    memcpy(opt.ssl_option.signpasswd,passwd.c_str(),passwd.length());
    memcpy(opt.server_addr,host.c_str(),host.length());
    int connection_num = 1;
    cspSessionPool * pc  = cspCreateSessionPool(&opt,connection_num);
    string str_message="jack";
    //下面操作开个线程，不加join，main线程退出后，子线程继续运行
    for (int i = 0; i < connection_num; i++)
    {
        std::thread t(&checkconnection,pc);
        t.detach();
    }
    while (true)
    {
        sleep(1);
    }
    cspFreeSessionPool(pc);
    return 0;
}


int mainxx(int argc , char *argv[])
{
    cspOptions opt;
    running=true;
    memset(&opt,0,sizeof(cspOptions));
    opt.type=NO_TLS;
    opt.logger_ptr=show_message11;
    string host=argv[1];
    opt.server_port = 30099;
    opt.connect_timeout=3000;
    opt.tcp_send_buffer=30720;
    opt.tcp_recv_buffer=30720;
    int max_run_time = atoi(argv[3]);
    int max_send_buffer = atoi(argv[4]);
     memcpy(opt.server_addr,host.c_str(),host.length());
    int max_thread = atoi(argv[2]);
    cspSessionPool * pc  = cspCreateSessionPool(&opt,max_thread);
    std::vector<std::thread *> pvec;
    for (int i = 0;i<max_thread;i++)
    {
        std::thread * t = new thread(&handle,pc,max_send_buffer,max_run_time);
        pvec.push_back(t);
    }
    int timestart=time(NULL);
    for (size_t i = 0; i < max_thread; i++)
    {
       pvec[i]->join();
    }
    int timeend=time(NULL);
    double total_bytes = max_thread*max_send_buffer*max_run_time;
    printf("totall %f\n",total_bytes);
    cout<<"total time:"<<timeend-timestart<<endl;
    int x = timeend-timestart;
    x=1024*1024*x;
    double sl = total_bytes/ x;
    printf("速率 %.2f Mbps\n",sl);
    cspFreeSessionPool(pc);
    return 0;
    
    /*
    cspOptions opt;
    memset(&opt,0,sizeof(cspOptions));
    opt.type=STD_TLS;
    string host="*************";
    opt.server_port = 1222;
    opt.connect_timeout=3000;
    //string certkeydir="/home/<USER>/key/gmssl_certs/certs/";
    //string cacert = certkeydir + "CA.pem";
    //cacert = certkeydir + "/home/<USER>/ngnix/Nginx-with-GmSSLv3-master/tools/cacert.pem";
    //string cacert = certkeydir + "client/demo1.sm2.trust.pem"; 
    //string cltsigncert = certkeydir + "client/c_sign.crt";
    //string cltsignkey = certkeydir + "client/c_signkey.pem";
    #if 0
    string certkeydir="/home/<USER>/key/gmssl_certs/certs/";
    string cacert = certkeydir + "CA.pem";
    string cltsigncert = certkeydir + "CS.pem";
    string cltsignkey = certkeydir + "CS.key";

    string cltencert = certkeydir + "CE.pem";
    string cltenkey = certkeydir + "CE.key";
    #else
    string certkeydir="/home/<USER>/key/client/";
    string cacert = certkeydir + "SMxCAforNAC.cer";
    string cltsigncert = certkeydir + "本地网关国密证书_sign.cer";
    string cltsignkey = certkeydir + "本地网关国密证书_sign.key";

    string cltencert = certkeydir + "本地网关国密证书_encry.cer";
    string cltenkey = certkeydir + "本地网关国密证书_encry.key";
    #endif 
    string str_p12="sm2.bigclient.sig.pfx";
    string str_pass="12345678";
    //memcpy(opt.ssl_option.cafile,cacert.c_str(),cacert.length());
    memcpy(opt.ssl_option.signcertfile,cltsigncert.c_str(),cltsigncert.length());
    memcpy(opt.ssl_option.signkeyfile,cltsignkey.c_str() ,cltsignkey.length());
    memcpy(opt.ssl_option.encertfile,cltencert.c_str(),cltencert.length());
    memcpy(opt.ssl_option.enkeyfile,cltenkey.c_str(),cltenkey.length());
    memcpy(opt.ssl_option.signp12,str_p12.c_str(),str_p12.length());
    memcpy(opt.ssl_option.signpasswd,str_pass.c_str(),str_pass.length());
    opt.ssl_option.verity_mode=1;
    opt.ssl_option.load_order=0;
    opt.type=GM_TLS;
    memcpy(opt.server_addr,host.c_str(),host.length());
  
    cspSessionPool * pc  = cspCreateSessionPool(&opt,5);
    int i=0;
    while(i++<=2)
    {
        cspSession *connection =  cspGetSessionFromPool(pc,200);
        if(connection)
        {
            printf("connect is ok\n");
        }
        else
        {
            printf("no connection ,now continue:[%d]\n",time(NULL));
            continue;
        }
        while(true)
        {
            char pdata[1024]={0};
            int rc = cspSend(connection,pdata,5,1000);
            int ret = cspRead(connection,pdata,5,5000);
            cspBackSessionToPool(pc,connection);
            break;
            printf("now ret:[%d]\n",ret);
            if(ret>0)
            {
                //printf("data:[%s]\n",pdata);
                
                
                break;
                //printf("rc = [%d]\n",rc);
            }
            if(ret<0)
            {
                printf("server is disconnected\n");
                cspBackSessionToPool(pc,connection);
                break;
            }
            
        }
    }
    cspFreeSessionPool(pc);
    */

    return 0;
}

