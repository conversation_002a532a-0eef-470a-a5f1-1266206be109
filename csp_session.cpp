#include "asio_tcp_client_pool.h"

cspShowMessage * g_logger_ptr = NULL;
//***************************************************************************
//功能：获取连接对象
//输入：cspOptions
//输出：cspSession
//***************************************************************************
cspSession *cspConnectWithOptions(const cspOptions *options)
{
	cspSession *c = new cspSession();
	c->type = options->type;
	memcpy(&c->csp_option, options, sizeof(cspOptions));
	g_logger_ptr = options->logger_ptr;
	sync_asio_tcp_client* tcp_client_ptr = new sync_asio_tcp_client((cspOptions *)options);
	int ret = tcp_client_ptr->connect();
	c->err = ret;
	c->privdata = (void *)tcp_client_ptr;
	return c;
}

//***************************************************************************
//功能：读取数据
//输入: 1:会话连接对象;2:数据缓存区;3:缓冲区长度;4、读取超时(毫秒，为0表示不设置超时)
//输出: 大于0读取成功，等于0未读取到数据，小于0失败(参考错误返回码)
//***************************************************************************
int cspRead(cspSession *c, char *pData, int len, int timeout_ms)
{
	if (c)
	{
		if (c->privdata)
		{
			//boost::shared_ptr<sync_asio_tcp_client> &tcp_client_ptr = (*(boost::shared_ptr<sync_asio_tcp_client>*)(c->privdata));
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			if (tcp_client_ptr)
			{
				return tcp_client_ptr->read(pData, len, timeout_ms);
			}
		}
	}
	return CSP_ERR;
}

//***************************************************************************
//功能：发送数据
//输入: 1:会话连接对象;2:数据;3:数据长度;4、写入超时(毫秒，为0表示不设置超时)
//输出: 等于0写入成功，小于0失败(参考错误返回码)
//***************************************************************************
int cspSend(cspSession *c, char *pData, int len, int timeout_ms)
{
	if (c)
	{
		if (c->privdata)
		{
			//boost::shared_ptr<sync_asio_tcp_client> &tcp_client_ptr = (*(boost::shared_ptr<sync_asio_tcp_client>*)(c->privdata));
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			if (tcp_client_ptr)
			{
				return tcp_client_ptr->write(pData, len, timeout_ms);
			}
		}
	}
	return CSP_ERR;
}

//***************************************************************************
//功能：检查会话连接状态
//输入: 会话连接对象
//输出: CSP_OK:连接正常，CSP_ERR连接失败
//***************************************************************************
int cspCheckConnection(cspSession *c)
{
	if (c)
	{
		if (c->privdata)
		{
			//boost::shared_ptr<sync_asio_tcp_client> &tcp_client_ptr = (*(boost::shared_ptr<sync_asio_tcp_client>*)(c->privdata));
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			if (tcp_client_ptr)
			{
				return tcp_client_ptr->check_connection();
			}
		}
	}
	return CSP_ERR;
}

//***************************************************************************
//功能：会话重连
//输入: 会话连接对象
//输出: CSP_OK:连接正常，CSP_ERR连接失败
//***************************************************************************
int cspReconnect(cspSession *c)
{
	if (c)
	{
		if (c->privdata)
		{
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			return tcp_client_ptr->connect();
		}
	}
	return CSP_ERR;
}

//***************************************************************************
//功能：断开连接
//输入: 会话连接对象
//输出: 无
//***************************************************************************
void cspDisconnect(cspSession *c)
{
	if (c)
	{
		if (c->privdata)
		{
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			if (tcp_client_ptr)
			{
				return tcp_client_ptr->close();
			}

		}
	}
}

//***************************************************************************
//功能：释放会话连接对象
//输入: 会话连接对象
//输出: 无
//***************************************************************************
void cspConnectionFree(cspSession *c)
{
	if (c)
	{
		if (c->privdata)
		{
			sync_asio_tcp_client* tcp_client_ptr = (sync_asio_tcp_client*)c->privdata;
			if (tcp_client_ptr)
			{
				tcp_client_ptr->close();
				delete tcp_client_ptr;
				tcp_client_ptr = NULL;

			}
		}
		delete c;
		c = NULL;
	}
}
//***************************************************************************
//功能：创建会话连接池
//输入: 配置，最大连接数
//输出: 无
//***************************************************************************
cspSessionPool *cspCreateSessionPool(const cspOptions *options, int max_sessions)
{
	cspSessionPool *csp_pool = new cspSessionPool();
	memcpy(&csp_pool->csp_option, options, sizeof(cspOptions));
	g_logger_ptr = options->logger_ptr;
	asio_tcp_client_pool* tcp_client_pool_ptr = new asio_tcp_client_pool((cspOptions *)options, max_sessions);
	csp_pool->privdata = tcp_client_pool_ptr;
	return csp_pool;
}

//***************************************************************************
//功能：从连接池获取一个连接对象
//输入: 会话连接池,超时时间
//输出: 连接对象
//***************************************************************************
cspSession * cspGetSessionFromPool(cspSessionPool *pc, int timeout_ms)
{
	if (pc)
	{
		if (pc->privdata)
		{
			cspSession * c = NULL;
			asio_tcp_client_pool* tcp_client_pool_ptr = (asio_tcp_client_pool*)pc->privdata;
			if (tcp_client_pool_ptr)
			{
				c = tcp_client_pool_ptr->getConnection(timeout_ms);
				pc->err = tcp_client_pool_ptr->getErrCode();
				if (c)
				{
					pc->err = CSP_OK;
				}
				return c;
			}
		}
	}
	return NULL;
}

//***************************************************************************
//功能：放回连接对象到连接池中
//输入: 连接池,连接对象
//输出: 无
//***************************************************************************
void cspBackSessionToPool(cspSessionPool *pc, cspSession *c)
{
	if (pc)
	{
		if (pc->privdata)
		{
			asio_tcp_client_pool* tcp_client_pool_ptr = (asio_tcp_client_pool*)pc->privdata;
			if (tcp_client_pool_ptr)
			{
				tcp_client_pool_ptr->backConnection(c);
			}
		}
	}
	return;
}

//***************************************************************************
//功能：释放会话连接池内存
//输入: 创建的会话连接池对象
//输出: 无
//***************************************************************************
void cspFreeSessionPool(cspSessionPool *pc)
{
	show_message(DEBUG, "cspFreeSessionPool [%p]", pc);
	boost::unique_lock<boost::mutex> lock(asio_tcp_client_pool::g_pool_mtx);//加上互斥锁
	if (pc)
	{
		if (pc->privdata)
		{
			asio_tcp_client_pool* tcp_client_pool_ptr = (asio_tcp_client_pool*)pc->privdata;
			if (tcp_client_pool_ptr)
			{
				tcp_client_pool_ptr->freePool();
				delete tcp_client_pool_ptr;
				tcp_client_pool_ptr = NULL;
			}
		}
		delete pc;
		pc = NULL;
	}
	return;
}