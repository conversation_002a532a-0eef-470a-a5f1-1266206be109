#ifndef __ML_KEM_H__
#define __ML_KEM_H__

#include <stdint.h>

typedef struct ml_kem_ctx_t {
    const void *info;
    uint32_t length_public_key;
    uint32_t length_secret_key;
    uint32_t length_ciphertext;
    uint32_t length_shared_secret;
} ML_KEM_Ctx;

typedef enum {
    MLKEM_512 = 0x1,
    MLKEM_768,
    MLKEM_1024,
} ML_KEM_TYPE;

ML_KEM_Ctx *ML_KEM_NewCtx(unsigned int type);
void ML_KEM_FreeCtx(ML_KEM_Ctx *ctx);

int ML_KEM_keypair(const ML_KEM_Ctx *ctx, unsigned char *public_key, unsigned char *secret_key);
int ML_KEM_encaps(const ML_KEM_Ctx *ctx, uint8_t *ciphertxt, uint8_t *share_secret, uint8_t *public_key);
int ML_KEM_decaps(const ML_KEM_Ctx *ctx, const uint8_t *ciphertext, uint8_t *shared_secret, uint8_t *secret_key);

#endif
