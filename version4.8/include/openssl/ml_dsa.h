#ifndef __ML_DSA_H__
#define __ML_DSA_H__

# include <openssl/opensslconf.h>
# include <stdint.h>
# include <stdbool.h>

# ifdef  __cplusplus
extern "C" {
# endif

typedef struct MlDsaCtx {
    const void *info;
    uint32_t length_public_key;
    uint32_t length_private_key;
    uint32_t length_signature;
    uint8_t *ctx;
    uint32_t ctx_len;
    bool is_mu_msg;
    bool need_encode_ctx;
    bool deterministic_sign_flag;
} ML_DSA_Ctx;

//api
enum ML_DSA_AlgId {
    MLDSA_44,
    MLDSA_65,
    MLDSA_87,
};

ML_DSA_Ctx *ML_DSA_NewCtx(int type);
void ML_DSA_FreeCtx(ML_DSA_Ctx *ctx);

int ML_DSA_keypair(ML_DSA_Ctx *ctx, uint8_t *public_key, uint8_t *private_key);
int ML_DSA_sign(ML_DSA_Ctx *ctx, uint8_t *private_key, const uint8_t *data, uint32_t dataLen, uint8_t *sign);
int ML_DSA_verify(ML_DSA_Ctx *ctx, uint8_t *public_key, const uint8_t *data, uint32_t dataLen, uint8_t *sign);

# ifdef __cplusplus
}
# endif

#endif
