#pragma once
/*
* Copyright (c) 2023，启明星辰
* All rights reserved.
*
* 文件名称：asio_tcp_client.h
* 文件标识：同步客户端连接对象
*
* 历史版本：1.0
* 作    者：高海清
* 初始化完成日期：2023年5月10日
* 修改日期：2024年11月5日
*/
#include <boost/asio.hpp>
#include <boost/bind.hpp>
#include <boost/shared_ptr.hpp>
#include <boost/asio/ssl.hpp>
#include <boost/enable_shared_from_this.hpp>
#include <boost/thread.hpp>
#include <boost/thread/mutex.hpp>
#include <boost/chrono.hpp>
#include <string>
#include <iostream>
#include <memory>
#include <iostream>
#include <queue>
#include "csp_session.h"
#ifdef WIN32
#include <atomic>
typedef std::atomic<int> ATOMIC_INT;
#else
#include <boost/atomic.hpp>
typedef boost::atomic<int> ATOMIC_INT;
#endif
using namespace std;
typedef boost::shared_ptr<boost::asio::io_context> io_context_ptr_t;
typedef boost::asio::ssl::stream< boost::asio::ip::tcp::socket > ssl_socket_t;
typedef boost::asio::ip::tcp::socket raw_socket_t;
typedef boost::asio::ssl::context ssl_context_t;
typedef boost::shared_ptr< ssl_context_t > ssl_context_ptr_t;
typedef boost::shared_ptr< ssl_socket_t  > ssl_socket_ptr_t;
typedef boost::shared_ptr< raw_socket_t  > raw_socket_ptr_t;


typedef boost::shared_ptr<boost::asio::deadline_timer> deadline_timer_ptr_t;
enum {
	DEBUG = 0,
	INFO,
	WARN,
	ERR,
	OFF
};
extern cspShowMessage * g_logger_ptr;//日志输出回调函数指针
class asio_message
{
public:
	enum
	{
		default_message_length = 1024,
		header_length = 36,
	};
	asio_message(int len)
	{
		pData = new char[len + 1];
		m_len = len;
	}
	asio_message()
	{
		pData = new char[default_message_length + 1];
		m_len = default_message_length;
	}
	~asio_message()
	{
		if (pData)
		{
			delete[]pData;
			pData = NULL;
		}
	}
public:
	char *data()
	{
		return pData;
	}
	int length()
	{
		return m_len;
	}
	int body_length()
	{
		return m_len;
	}
	char* body()
	{
		return pData;
	}
private:
	char *pData;
	int m_len;
};
#define show_message(level, format, ...) \
	if (NULL == g_logger_ptr) \
	{ \
		fprintf(stderr, format "\n", ##__VA_ARGS__); \
	} \
	else \
	{ \
		g_logger_ptr(level,format,##__VA_ARGS__) ;\
	} \
    	
		
typedef boost::shared_ptr<asio_message> asio_message_ptr_t;
class sync_asio_tcp_client
{
public:
	sync_asio_tcp_client(cspOptions* option);
	// 新增构造函数，接受外部io_context以减少文件句柄占用
	sync_asio_tcp_client(cspOptions* option, io_context_ptr_t shared_io_context);
	~sync_asio_tcp_client();
public:
	//***************************************************************************
	//功能：连接服务器
	//输入: 无
	//输出: 参考错误返回码
	//***************************************************************************
	int connect();

	//***************************************************************************
	//功能：读取数据
	//输入: 1:数据缓冲区;2:需读取的数据长度(直到读取完或超时才返回);3、读取超时(毫秒，为0表示不设置超时)
	//输出: 0:读取超时，<0失败(参考错误返回码)，>0读取的数据长度
	//***************************************************************************
	int read(char* data, const int len, int timeout);

	//***************************************************************************
	//功能：发送数据
	//输入: 1:数据;2:数据长度;3、写入超时(毫秒，为0表示不设置超时)
	//输出: 等于0写入成功，小于0失败(参考错误返回码)
	//***************************************************************************
	int write(const char* data, const int len, int timeout);

	//***************************************************************************
	//功能：关闭连接
	//输入: 无
	//输出: 无
	//***************************************************************************
	void close();
	//***************************************************************************
	//功能：检查连接状态
	//输入: 超时时间(毫秒)
	//输出: 0:连接
	//***************************************************************************
	int check_connection(int timeout = 100);
private:
	int init();
	int ssl_connect();
	int raw_connect();
	bool verify_certificate(bool preverified, boost::asio::ssl::verify_context& ctx);
	void handle_connect(const boost::system::error_code& ec, boost::system::error_code* out_ec);
	int handshake();
	void handle_handshake(const boost::system::error_code& ec, boost::system::error_code* out_ec);
	ssl_socket_t::lowest_layer_type& sslsocket();
	void check_deadline();
	void handle_write(asio_message_ptr_t message_ptr, const boost::system::error_code& error, size_t length,
		boost::system::error_code* out_ec, std::size_t* out_length);
	void handle_read(const boost::system::error_code& ec, std::size_t length,
		boost::system::error_code* out_ec, std::size_t* out_length);
	int set_sign_file();
	int set_en_file();
	int set_sign_p12();
	int set_en_p12();
	int SetKeepAlive();
	//by jack 2024/11/添加socks代理协议
	int socks_proxy_connect();
	int do_socks_none_auth();
	int do_socks_pwd_auth();
	int do_socks_type_connect();
	int do_socks_write(const char* data, const int len, int timeout);
	int do_socks_read(char* data, const int len, int timeout);
public:
	deadline_timer_ptr_t deadline_ptr;//定时器
	raw_socket_ptr_t raw_socket_ptr;  //普通tcp传输对象
	ssl_context_ptr_t ssl_context_ptr; //加密上下文
	ssl_socket_ptr_t ssl_socket_ptr;//加密tcp传输对象
	io_context_ptr_t io_service_ptr;//io服务
	cspOptions csp_option;//配置
	bool is_connected;
	std::string local_address_;
	boost::shared_ptr<asio_message> new_write_msg;
	boost::asio::strand<boost::asio::io_context::executor_type> strand_;


};
typedef boost::shared_ptr<sync_asio_tcp_client> sync_asio_tcp_client_ptr_t;