#include "asio_tcp_client_pool.h"
boost::mutex asio_tcp_client_pool::g_pool_mtx  ;
asio_tcp_client_pool::asio_tcp_client_pool(cspOptions* option,int max_connections):currentConnectionNum(0)
{
	current_err_code = CSP_ERR;
    memcpy(&csp_option,option,sizeof(csp_option));
    maxConnectionNum = max_connections;
    
    // 创建共享的io_context，所有连接将共享这个io_context以减少文件句柄占用
    shared_io_context_.reset(new boost::asio::io_context());
    // 注意：deadline_timer不能共享，每个连接需要独立的定时器以避免超时冲突
    show_message(DEBUG,"now create cspSessionPool,size:[%d], shared io_context created",maxConnectionNum);
}
asio_tcp_client_pool::~asio_tcp_client_pool()
{
    show_message(DEBUG,"now delete cspSessionPool");
    // 清理共享的资源
    if (shared_io_context_.get()) {
        shared_io_context_.reset();
        show_message(DEBUG,"shared io_context released");
    }
}
cspSession* asio_tcp_client_pool::getConnection(int ms)
{
    //boost::this_thread::sleep(boost::posix_time::milliseconds(100));
    boost::unique_lock<boost::mutex> lock(mutex);//加上互斥锁
    show_message(DEBUG,"current size=[%d],ms=[%d]\n",currentConnectionNum.load(),ms);
    /*
    while (!connections.empty()) 
    {
        cspSession* c =  connections.front();
        sync_asio_tcp_client *client_ptr =  (sync_asio_tcp_client*)c->privdata;
        if(client_ptr->check_connection()!=CSP_OK)
        {
            client_ptr->close();//关闭无效连接
            connections.pop();//弹出队
            delete client_ptr;
            client_ptr=NULL;
            delete c;
            c=NULL;
            currentConnectionNum--;
            
        }
        else
        {
            break;
        }
    }
    */
    //进行一番尝试之后,接下来,如果队列不为空,则队首连接是有效连接
    if (!connections.empty()) {
        return popConnection();
    }
    //接下来是队列为空的情况
    //判断是否已经大于最大连接数
    if (currentConnectionNum < maxConnectionNum) {
        //若当前的连接数还没到最大连接数,则创建新的连接
        return createConnection();
    }
    show_message(DEBUG,"ConnectionPool is full,now wait for\n");
    /*
    if (!conditionVariable.wait_for(lock, std::chrono::milliseconds(ms), [this]() {
        return !connections.empty();
    })) {
        //如果还是空则返回NULL
        return NULL;
    }
    */
    if(!conditionVariable.timed_wait(lock, boost::posix_time::milliseconds(ms))){
        if (connections.empty()){
            return NULL;
        }
    }
    //等待成功了,则获取到那个最新的连接
    return popConnection();
}
void asio_tcp_client_pool::backConnection(cspSession* c)
{
    show_message(DEBUG,"backConnection begin [%p] [%zu]",c,connections.size());
    if (c != NULL) {
        boost::unique_lock<boost::mutex> lock(mutex);//加上互斥锁
        connections.push(c);
        conditionVariable.notify_one();//释放互斥锁，通知等待线程
    }
    show_message(DEBUG,"backConnection end [%p] [%zu]",c,connections.size());
}

cspSession* asio_tcp_client_pool::popConnection()
{
    if (connections.empty()) 
    {
        return NULL;
    }
    cspSession *c = connections.front();
    connections.pop();
    return c;
}
cspSession* asio_tcp_client_pool::createConnection()
{
    cspSession *c=NULL;
    // 为了避免多线程共享io_context导致的竞态条件，
    // 在多线程环境下为每个连接创建独立的io_context
    sync_asio_tcp_client* tcp_client_ptr = nullptr;

    if (maxConnectionNum > 1) {
        // 多连接情况下，使用独立的io_context避免竞态条件
        tcp_client_ptr = new sync_asio_tcp_client((cspOptions *)&csp_option,shared_io_context_);
        show_message(DEBUG,"creating connection with independent io_context for thread safety");
    } else {
        // 单连接情况下，可以安全使用共享io_context
        tcp_client_ptr = new sync_asio_tcp_client((cspOptions *)&csp_option, shared_io_context_);
        show_message(DEBUG,"creating connection with shared io_context");
    }

    int ret = tcp_client_ptr->connect();
    current_err_code = ret;
    if(ret!=CSP_OK)
    {
        delete tcp_client_ptr;
        tcp_client_ptr=NULL;
        return c;
    }
    c = new cspSession();
    c->type = csp_option.type;
    memcpy(&c->csp_option,&csp_option,sizeof(cspOptions));
    c->err = ret;
    c->privdata =(void *) tcp_client_ptr;
    //记上当前连接数
    currentConnectionNum++;
    show_message(DEBUG,"now currentCspSessionNum:[%d]",currentConnectionNum.load());
    //连接上,返回
    return c;
}
void asio_tcp_client_pool::freePool()
{
    show_message(DEBUG,"now freecspSessionPool:[%d]",currentConnectionNum.load());
    boost::unique_lock<boost::mutex> lock(mutex);//加上互斥锁
    while (!connections.empty()) 
    {
        cspSession *c =  popConnection();
        show_message(DEBUG,"freePool free  [%p] begin",c);
        cspConnectionFree(c);
        currentConnectionNum--;
    }
}