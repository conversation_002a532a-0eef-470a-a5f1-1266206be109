#include "asio_tcp_client.h"
#include "tools.h"
using namespace std;
#ifdef WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#define SIO_KEEPALIVE_VALS _WSAIOW(IOC_VENDOR,4)
struct TCP_KEEPALIVE_S
{
	unsigned long onoff;
	unsigned long keepalivetime;//开始首次KeepAlive探测前的TCP空闭时间
	unsigned long keepaliveinterval;//两次KeepAlive探测间的时间间隔
};
#endif
#define MAX_BUF_SIZE 16384

#define SOCKS5_VERSION 0x05
#define NO_AUTHENTICATION 0x00
#define PWD_AUTHENTICATION 0X02
#define CONNECT_COMMAND 0x01
#define IPV4_ADDRESS_TYPE 0x01

#ifdef VER4
	template <typename T>
	std::string TO_STRING(T value) {
		std::ostringstream oss;
		oss << value;
		return oss.str();
	}

#else
	#define TO_STRING(x) std::to_string(x)
#endif


inline int addr2dec(int type, char *addr,unsigned char* out_dec){
	int ret = 0;
#ifdef WIN32
	switch(type){
		case AF_INET:
			struct in_addr ipv4addr;
			// 使用 InetPton 转换 IPv4 地址
			if (InetPtonA(AF_INET, addr, &ipv4addr) == 1) {
				unsigned int ipv4_dec = ipv4addr.s_addr;
				memcpy(out_dec, &ipv4_dec, 4);
			} else {
				printf("Invalid IPv4 address.\n");
				ret =  2;
			}
			break;
		case AF_INET6:
			struct in6_addr ipv6addr;
			// 使用 InetPton 转换 IPv6 地址
			if (InetPtonA(AF_INET6, addr, &ipv6addr) == 1) {
				memcpy(out_dec, ipv6addr.s6_addr, 16);
			} else {
				printf("Invalid IPv6 address.\n");
				ret = 3;
			}
			break;
	}
#else
		//linux下转换
		switch(type){
		case AF_INET:
			if (inet_pton(AF_INET,addr, (void*)out_dec) != 1) {
				printf("Invalid IPv4 address.\n");
				ret =  2;
			}
			break;
		case AF_INET6:
    		if (inet_pton(AF_INET6, addr, (void *)out_dec) != 1) {
				printf("Invalid IPv6 address.\n");
				ret =  3;
			}
			break;
		}
		
#endif	
	return ret;
}


sync_asio_tcp_client::sync_asio_tcp_client(cspOptions* option)
{
	is_connected = false;
	memcpy(&csp_option, option, sizeof(csp_option));
	if(csp_option.tcp_recv_buffer<=0)
	{
		csp_option.tcp_recv_buffer = MAX_BUF_SIZE;
	}
	if(csp_option.tcp_send_buffer<=0)
	{
		csp_option.tcp_send_buffer = MAX_BUF_SIZE;
	}
	show_message(DEBUG, "new tcp_client success [%p]", this);
	ssl_socket_ptr.reset();
	raw_socket_ptr.reset();
	deadline_ptr.reset();
}

// 新增构造函数，接受外部io_context以减少文件句柄占用
sync_asio_tcp_client::sync_asio_tcp_client(cspOptions* option, io_context_ptr_t shared_io_context)
{
	is_connected = false;
	memcpy(&csp_option, option, sizeof(csp_option));
	if(csp_option.tcp_recv_buffer<=0)
	{
		csp_option.tcp_recv_buffer = MAX_BUF_SIZE;
	}
	if(csp_option.tcp_send_buffer<=0)
	{
		csp_option.tcp_send_buffer = MAX_BUF_SIZE;
	}
	
	// 使用外部提供的共享io_context，减少文件句柄占用
	io_service_ptr = shared_io_context;
	show_message(DEBUG, "new tcp_client with shared io_context success [%p]", this);
	
	ssl_socket_ptr.reset();
	raw_socket_ptr.reset();
	deadline_ptr.reset();
}

sync_asio_tcp_client::~sync_asio_tcp_client()
{
	close();
	boost::system::error_code err;
	if (deadline_ptr.get())
	{
		deadline_ptr->cancel(err);
	}
	//必须按顺序来进行是否可能会抛出异常
	ssl_context_ptr.reset();
	raw_socket_ptr.reset();
	ssl_socket_ptr.reset();
	show_message(DEBUG, "free tcp_client sucess [%p]", this);
}

int sync_asio_tcp_client::connect()
{
	close();
	int ret  = -1;
	try{
		ret = init();
	}
	catch(std::exception &ec){
		return CSP_INIT_SSL_ERR;
	}
	if (ret != CSP_OK)
	{
		return ret;
	}
	if (csp_option.type == NO_TLS)
	{
		ret = raw_connect();
	}
	else
	{
		ret = ssl_connect();
	}
	if (ret != CSP_OK)
	{
		show_message(DEBUG, "connect server:%s:%d false...", csp_option.server_addr, csp_option.server_port);
	}
	else
	{
		
		show_message(DEBUG, "connect server:%s:%d success...", csp_option.server_addr, csp_option.server_port);
		try
		{
			SetKeepAlive();
		}
		catch (...)
		{
			show_message(DEBUG, "SetKeepAlive failed...");
			close();
			return CSP_ERR;
		}

	}
	
	return ret;
}
int sync_asio_tcp_client::read(char* data, const int len, int timeout)
{
	boost::posix_time::time_duration td;
	if (timeout == 0){
		td = boost::date_time::pos_infin;
	}else{
		td = boost::posix_time::milliseconds(timeout);
	}
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	size_t length = 0;
	if (csp_option.type == NO_TLS){
		if (!raw_socket_ptr.get() || !raw_socket_ptr->is_open()){
			show_message(WARN, "rawsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_read(*raw_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_read,
			this,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}else{
		if (!ssl_socket_ptr.get() || !sslsocket().is_open()){
			show_message(WARN, "[read]:sslsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_read(*ssl_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_read,
			this,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}

	boost::system::error_code err;
	do{
		io_service_ptr->run_one(err);
	} while (ec == boost::asio::error::would_block);
	if (ec){
		//超时
		if (ec == boost::asio::error::operation_aborted){
			return 0;
		}else{
			//连接断开
			close();
			show_message(ERR, "[read]:read error,maybe server is disconnect,now close");
			return CSP_DISCONNECT;
		}
	}
	return length;
}
int sync_asio_tcp_client::write(const char* data, const int len, int timeout)
{
	boost::posix_time::time_duration td;
	if (timeout == 0){
		td = boost::date_time::pos_infin;
	}else{
		td = boost::posix_time::milliseconds(timeout);
	}
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	size_t length = 0;
	if (csp_option.type == NO_TLS){
		if (!raw_socket_ptr.get() || !raw_socket_ptr->is_open()){
			show_message(WARN, "[write]:rawsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_write(*raw_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_write,
			this,
			new_write_msg,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
			
	}
	else{
		if (!ssl_socket_ptr.get() || !sslsocket().is_open()){
			show_message(WARN, "[write]:sslsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_write(*ssl_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_write,
			this,
			new_write_msg,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}

	boost::system::error_code err;
	do{
		io_service_ptr->run_one(err);
		if (err){
			break;
		}
	} while (ec == boost::asio::error::would_block);
	if (ec){
		close();
		show_message(ERR, "[send] send date false,maybe server is disconnected,now close");
		return CSP_ERR;
	}
	return CSP_OK;
}
int sync_asio_tcp_client::ssl_connect()
{
	string host = csp_option.server_addr;
	string service = TO_STRING(csp_option.server_port);
	if(csp_option.use_socks_proxy){
		host = csp_option.socks_options.proxy_addr;
		service = TO_STRING(csp_option.socks_options.proxy_port);
		memset(csp_option.server_addr,0,sizeof(csp_option.server_addr));
		memcpy(csp_option.server_addr,host.c_str(),host.length());
		csp_option.server_port = csp_option.socks_options.proxy_port;
	}
	boost::system::error_code err;
	boost::asio::ip::tcp::resolver::query query(host, service);
	boost::asio::ip::tcp::resolver::iterator iter = boost::asio::ip::tcp::resolver(*io_service_ptr).resolve(query, err);
	if (err){
		return CSP_ERR;
	}
	if (csp_option.connect_timeout == 0){
		csp_option.connect_timeout = 10000;
	}
	boost::posix_time::time_duration td;
	td = boost::posix_time::milliseconds(csp_option.connect_timeout);
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	boost::asio::async_connect(sslsocket(), iter, boost::bind(&sync_asio_tcp_client::handle_connect, this, boost::asio::placeholders::error, &ec));
	do{
		io_service_ptr->run_one(err);
		if (err){
			break;
		}
	} while (ec == boost::asio::error::would_block);
	if (ec || !sslsocket().is_open()){
		return CSP_ERR;
	}
	//在handshake前使用socks5代理模式
	if(csp_option.use_socks_proxy){
		int ret = socks_proxy_connect();
		if (ret != CSP_OK){
			close();
			return CSP_ERR;
		}
	}
	int ret = CSP_ERR;
	try{
		ret = handshake();
	}catch (...){
		std::cerr << "handshake error" << '\n';
	}
	return ret;

}
int sync_asio_tcp_client::raw_connect()
{
	string host = csp_option.server_addr;
	string service = TO_STRING(csp_option.server_port);
	if(csp_option.use_socks_proxy){
		host = csp_option.socks_options.proxy_addr;
		service = TO_STRING(csp_option.socks_options.proxy_port);
		memset(csp_option.server_addr,0,sizeof(csp_option.server_addr));
		memcpy(csp_option.server_addr,host.c_str(),host.length());
		csp_option.server_port = csp_option.socks_options.proxy_port;
	}
	boost::asio::ip::tcp::resolver::query query(host, service);
	boost::system::error_code err;
	boost::asio::ip::tcp::resolver::iterator iter = boost::asio::ip::tcp::resolver(*io_service_ptr).resolve(query, err);
	if (err){
		return CSP_ERR;
	}
	if (csp_option.connect_timeout == 0){
		csp_option.connect_timeout = 10000;
	}
	boost::posix_time::time_duration td;
	td = boost::posix_time::milliseconds(csp_option.connect_timeout);
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	boost::asio::async_connect(*raw_socket_ptr, iter, boost::bind(&sync_asio_tcp_client::handle_connect, this, boost::asio::placeholders::error, &ec));
	do{
		io_service_ptr->run_one(err);
		if (err){
			break;
		}
	} while (ec == boost::asio::error::would_block);
	if (ec || !raw_socket_ptr->is_open()){
		show_message(ERR, "connection failed: %s", ec.message().c_str());
		return CSP_ERR;
	}
	boost::asio::ip::tcp::endpoint et = raw_socket_ptr->local_endpoint(err);
	if (!err){
		local_address_ = et.address().to_string(err);
	}
	is_connected = true;
	//使用socks代理
	if(csp_option.use_socks_proxy){
		int ret =  socks_proxy_connect();
		if (ret != CSP_OK){
			close();
			return CSP_ERR;
		}
	}
	return CSP_OK;
}
bool sync_asio_tcp_client::verify_certificate(bool preverified,
	boost::asio::ssl::verify_context& ctx)
{
	char subject_name[256] = { 0 };
	X509* cert = X509_STORE_CTX_get_current_cert(ctx.native_handle());
	X509_NAME_oneline(X509_get_subject_name(cert), subject_name, 256);
	show_message(DEBUG, "[verify_certificate]:[%d],[%s]", preverified, subject_name);
	return preverified;
}
int sync_asio_tcp_client::init()
{
	int ret = CSP_ERR;
	
	if (csp_option.type == NO_TLS)
	{
		if (!raw_socket_ptr.get())
		{
			show_message(DEBUG,"init raw_tcp_socket_t");
			// 如果io_service_ptr为空，则创建新的io_context（向后兼容）
			if (!io_service_ptr.get())
			{
				printf("new io_service_ptr\n");
				io_service_ptr.reset(new boost::asio::io_context());
			}
			raw_socket_ptr.reset(new raw_socket_t(*io_service_ptr));
			deadline_ptr.reset(new boost::asio::deadline_timer(*io_service_ptr));
		}

	}
	else
	{
		if (!ssl_socket_ptr.get())
		{
			
			if(!io_service_ptr.get())
			{
				io_service_ptr.reset(new boost::asio::io_context());
			}
			if(!deadline_ptr.get())
			{
				deadline_ptr.reset(new boost::asio::deadline_timer(*io_service_ptr));
			}
			if(!ssl_context_ptr.get())
			{	
				if (csp_option.type == GM_TLS){
					ssl_context_ptr.reset(new ssl_context_t(boost::asio::ssl::context::gmtls_client));
				}else{
					//设置同时支持tls1.2和tls1.3,兼容pqc量子加密tls1.3
					ssl_context_ptr.reset(new ssl_context_t(boost::asio::ssl::context::tlsv12_client));
					ssl_context_ptr->set_options(boost::asio::ssl::context::tlsv12_client|boost::asio::ssl::context::tlsv13_client);
				}
				try
				{
					if (csp_option.ssl_option.load_order == 1)
					{
						//ret = set_en_file();
						ret = set_en_file();
						if(ret!=CSP_OK)
						{
							return ret;
						}
						ret = set_sign_file();
						if(ret!=CSP_OK)
						{
							return ret;
						}
						if (strlen(csp_option.ssl_option.encp12)>0)
						{
							ret = set_en_p12();
							if (ret != CSP_OK)
							{
								return ret;
							}
						}

						if (strlen(csp_option.ssl_option.signp12)>0)
						{
							ret = set_sign_p12();
							if (ret != CSP_OK)
							{
								return ret;
							}
						}
					}
					else
					{
						ret = set_sign_file();
						if(ret!=CSP_OK)
						{
							return ret;
						}
						ret = set_en_file();
						if(ret!=CSP_OK)
						{
							return ret;
						}
						
						if (strlen(csp_option.ssl_option.signp12)>0)
						{
							ret = set_sign_p12();
							if (ret != CSP_OK)
							{
								return ret;
							}
						}
						if (strlen(csp_option.ssl_option.encp12)>0)
						{
							ret = set_en_p12();
							if (ret != CSP_OK)
							{
								return ret;
							}
						}
					}
				}
				catch (const std::exception& e)
				{
					show_message(ERR, "[init]:load cert file error");
					return CSP_LOAD_CERTKEY_ERR;
				}
				if (strlen(csp_option.ssl_option.cafile) > 0)
				{
					ssl_context_ptr->load_verify_file(csp_option.ssl_option.cafile);
				}
				
				if (csp_option.ssl_option.verity_mode == 0){
					ssl_context_ptr->set_verify_mode(boost::asio::ssl::verify_none);
				}else{
					ssl_context_ptr->set_verify_mode(boost::asio::ssl::verify_peer |boost::asio::ssl::verify_fail_if_no_peer_cert);
				}
				ssl_context_ptr->set_verify_callback(boost::bind(&sync_asio_tcp_client::verify_certificate,
			this, _1, _2));
			}
			ssl_context_ptr->set_verify_depth(1);
			ssl_socket_ptr.reset(new ssl_socket_t(*io_service_ptr, *ssl_context_ptr));
			//设置加密套件
			if (strlen(csp_option.ssl_option.ciphers) > 0){
				show_message(DEBUG,"set_ciphers:[%s]", csp_option.ssl_option.ciphers);
				SSL_CTX_set_cipher_list(ssl_context_ptr->native_handle(), csp_option.ssl_option.ciphers);
			}
			//设置密钥交换算法组
			if (strlen(csp_option.ssl_option.groups) > 0){
				show_message(DEBUG,"set_groups:[%s]", csp_option.ssl_option.groups);
				SSL_CTX_set1_groups_list(ssl_context_ptr->native_handle(), csp_option.ssl_option.groups);
			}
			if (csp_option.type == STD_TLS){
				show_message(DEBUG,"init tls_tcp_socket_t");
			}else{
				show_message(DEBUG,"init gmtls_tcp_socket_t");
				if (strlen(csp_option.ssl_option.ciphers) == 0){
					show_message(DEBUG,"set_default_ciphers:[ECDHE_SM4_GCM_SM3]");
					SSL_CTX_set_cipher_list(ssl_context_ptr->native_handle(), "ECDHE_SM4_GCM_SM3");
				}
				if (strlen(csp_option.ssl_option.groups) == 0){
					show_message(DEBUG,"set_default_groups:[X25519-ML-KEM-768]");
					SSL_CTX_set1_groups_list(ssl_context_ptr->native_handle(), "X25519-ML-KEM-768");
				}
			}
		}
	}
	deadline_ptr->expires_at(boost::posix_time::pos_infin);
	check_deadline();
	return CSP_OK;
}
void sync_asio_tcp_client::handle_connect(const boost::system::error_code& ec, boost::system::error_code* out_ec)
{
	*out_ec = ec;
}
int sync_asio_tcp_client::handshake()
{
	boost::posix_time::time_duration td;
	td = boost::posix_time::milliseconds(3000);
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	ssl_socket_ptr->async_handshake(boost::asio::ssl::stream_base::client,
		boost::bind(&sync_asio_tcp_client::handle_handshake,
		this,
		boost::asio::placeholders::error, &ec));
	boost::system::error_code err;
	do
	{
		io_service_ptr->run_one(err);
	} while (ec == boost::asio::error::would_block);
	if (ec || !sslsocket().is_open()){
		close();
		show_message(ERR, "handshake error:[%s]\n",ec.message().c_str());
		return CSP_HANDSHAKE_ERR;
	}
	is_connected = true;
	return CSP_OK;
}
void sync_asio_tcp_client::handle_handshake(const boost::system::error_code& ec, boost::system::error_code* out_ec)
{
	*out_ec = ec;
}
ssl_socket_t::lowest_layer_type& sync_asio_tcp_client::sslsocket()
{
	return ssl_socket_ptr->lowest_layer();
	
}
void sync_asio_tcp_client::check_deadline()
{
	if (deadline_ptr->expires_at() <= boost::asio::deadline_timer::traits_type::now())
	{
		boost::system::error_code ignored_ec;
		if (csp_option.type == NO_TLS){
			raw_socket_ptr->cancel(ignored_ec);
		}else{
			
			sslsocket().cancel(ignored_ec);
		}
		deadline_ptr->expires_at(boost::posix_time::pos_infin);
	}
	deadline_ptr->async_wait(boost::bind(&sync_asio_tcp_client::check_deadline, this));
}
void sync_asio_tcp_client::handle_write(boost::shared_ptr<asio_message> message_ptr, const boost::system::error_code& error, size_t length,
	boost::system::error_code* out_ec, std::size_t* out_length)
{
	*out_ec = error;
	*out_length = length;
}
void sync_asio_tcp_client::handle_read(const boost::system::error_code& ec, std::size_t length,
	boost::system::error_code* out_ec, std::size_t* out_length)
{
	*out_ec = ec;
	*out_length = length;
}
void sync_asio_tcp_client::close()
{
	if (csp_option.type == NO_TLS)
	{
		if (raw_socket_ptr.get())
		{
			boost::system::error_code err;
			if (raw_socket_ptr->is_open())
			{
				raw_socket_ptr->cancel(err);
				raw_socket_ptr->shutdown(boost::asio::ip::tcp::socket::shutdown_both, err);
				raw_socket_ptr->close(err);
				deadline_ptr->cancel(err);
			}
			deadline_ptr->cancel(err);
		}

	}
	else
	{
		boost::system::error_code ignored_ec;
		//首先必须cancel执行任何可能的未完成操作，然后再启动shutdown和close套接字。
		if (ssl_socket_ptr.get())
		{
			if (sslsocket().is_open())
			{
				sslsocket().cancel(ignored_ec);
				sslsocket().shutdown(boost::asio::ip::tcp::socket::shutdown_both, ignored_ec);
				sslsocket().close(ignored_ec);
				deadline_ptr->cancel(ignored_ec);
			}
			ssl_socket_ptr.reset();
			deadline_ptr->cancel(ignored_ec);
		}

	}
	//boost::this_thread::sleep(boost::posix_time::milliseconds(50));
}
int sync_asio_tcp_client::check_connection(int timeout)
{
	char buf[2];
    return read(buf, 2, timeout);
}
int sync_asio_tcp_client::set_sign_file()
{
	if (strlen(csp_option.ssl_option.signcertfile) > 0){
		show_message(DEBUG,"set_sign_file signcertfile :[%s]", csp_option.ssl_option.signcertfile);
		ssl_context_ptr->use_certificate_file(csp_option.ssl_option.signcertfile, boost::asio::ssl::context::pem);
	}
	if (strlen(csp_option.ssl_option.signkeyfile) > 0){
		show_message(DEBUG,"set_sign_file signcertfile :[%s]", csp_option.ssl_option.signkeyfile);
		ssl_context_ptr->use_private_key_file(csp_option.ssl_option.signkeyfile, boost::asio::ssl::context::pem);
		//检查用户私钥是否正确
		if (!SSL_CTX_check_private_key(ssl_context_ptr->native_handle())){
			show_message(DEBUG,"set_en_file error");
			return CSP_SIGNKEY_NOT_MATCH;
		}
	}
	return CSP_OK;
}
int sync_asio_tcp_client::set_en_file()
{
	if (csp_option.type == STD_TLS)
	{
		return CSP_OK;
	}
	if (strlen(csp_option.ssl_option.encertfile) > 0)
	{
		show_message(DEBUG,"set_en_file signcertfile :[%s]", csp_option.ssl_option.encertfile);
#ifndef OPENSSL_NO_CNSM   
		if (!SSL_CTX_use_enc_certificate_file(ssl_context_ptr->native_handle(), csp_option.ssl_option.encertfile, SSL_FILETYPE_PEM))
#else
		if (!SSL_CTX_use_certificate_file(ssl_context_ptr->native_handle(), csp_option.ssl_option.encertfile, SSL_FILETYPE_PEM))
#endif
		{
			show_message(DEBUG,"certificate_file load false\n");
			return CSP_LOAD_CERTKEY_ERR;
		}
	}
	if (strlen(csp_option.ssl_option.enkeyfile) > 0)
	{
		show_message(DEBUG,"set_en_file encertfile :[%s]", csp_option.ssl_option.enkeyfile);
#ifndef OPENSSL_NO_CNSM   
		if (!SSL_CTX_use_enc_PrivateKey_file(ssl_context_ptr->native_handle(), csp_option.ssl_option.enkeyfile, SSL_FILETYPE_PEM))
#else
		if (!SSL_CTX_use_PrivateKey_file(ssl_context_ptr->native_handle(), csp_option.ssl_option.enkeyfile, SSL_FILETYPE_PEM))
#endif
		{
			show_message(DEBUG,"PrivateKey load false\n");
			return CSP_LOAD_CERTKEY_ERR;
		}
			//检查用户私钥是否正确
	#ifndef OPENSSL_NO_CNSM   
		if (!SSL_CTX_check_enc_private_key(ssl_context_ptr->native_handle()))
	#else
		if (!SSL_CTX_check_private_key(ssl_context_ptr->native_handle()))
	#endif
		{
			//printf("set_en_file error\n");
			show_message(DEBUG,"PrivateKey not match\n");
			return CSP_ENKEY_NOT_MATCH;
		}
	}

	return CSP_OK;
}
int sync_asio_tcp_client::set_sign_p12()
{
	EVP_PKEY *key = NULL;
	X509 *cert = NULL;
	STACK_OF(X509) *ca = NULL;
	int ret = p12ExportCertKey(csp_option.ssl_option.signp12, csp_option.ssl_option.signpasswd,
		&cert, &key, &ca);
	if (ret == CSP_OK)
	{
		if (cert == NULL || key == NULL)
		{
			show_message(ERR, "sign p12 file export key or cert NULL");
			ret = CSP_ERR;
			goto end;
		}
		if (SSL_CTX_use_certificate(ssl_context_ptr->native_handle(), cert) <= 0)
		{
			show_message(ERR, "sign p12 use_certificate error");
			ret = CSP_ERR;
			goto end;

		}
		if (SSL_CTX_use_PrivateKey(ssl_context_ptr->native_handle(), key) <= 0)
		{
			show_message(ERR, "sign p12 use_PrivateKey error");
			ret = CSP_ERR;
			goto end;
		}

		if (SSL_CTX_check_private_key(ssl_context_ptr->native_handle()) <= 0)
		{
			show_message(ERR, "sign p12 check PrivateKey error");
			ret = CSP_SIGNKEY_NOT_MATCH;
			goto end;
		}

	}
	else
	{
		show_message(ERR, "pks12 parse error");
	}
end:
	p12FreeCertKey(cert, key, ca);
	return ret;
}
int sync_asio_tcp_client::set_en_p12()
{
	if (csp_option.type == STD_TLS)
	{
		return CSP_OK;
	}
	EVP_PKEY *key = NULL;
	X509 *cert = NULL;
	STACK_OF(X509) *ca = NULL;
	int ret = p12ExportCertKey(csp_option.ssl_option.encp12, csp_option.ssl_option.encpasswd,
		&cert, &key, &ca);
	if (ret == CSP_OK)
	{
		if (cert == NULL || key == NULL)
		{
			show_message(ERR, "enc p12 file export key or cert NULL");
			ret = CSP_ERR;
			goto end;
		}
#ifndef OPENSSL_NO_CNSM   
		if (SSL_CTX_use_enc_certificate(ssl_context_ptr->native_handle(), cert) <= 0)
#else
		if (!SSL_CTX_use_certificate(ssl_context_ptr->native_handle(), cert) <= 0)
#endif
		{
			show_message(ERR, "enc p12 use_certificate error");
			ret = CSP_ERR;
			goto end;
		}
#ifndef OPENSSL_NO_CNSM   
		if (SSL_CTX_use_enc_PrivateKey(ssl_context_ptr->native_handle(), key) <= 0)
#else
		if (!SSL_CTX_use_PrivateKey(ssl_context_ptr->native_handle(), key) <= 0)
#endif                                     
		{
			show_message(ERR, "enc p12 use_PrivateKey error");
			ret = CSP_ERR;
			goto end;
		}
		//检查用户私钥是否正确
#ifndef OPENSSL_NO_CNSM   
		if (SSL_CTX_check_enc_private_key(ssl_context_ptr->native_handle()) <= 0)
#else
		if (SSL_CTX_check_private_key(ssl_context_ptr->native_handle()) <= 0)
#endif
		{
			show_message(ERR, "enc p12 check PrivateKey error");
			ret = CSP_ENKEY_NOT_MATCH;
			goto end;
		}

	}
	else
	{
		show_message(ERR, "enc p12 export cert and key error");
	}
end:
	p12FreeCertKey(cert, key, ca);
	return ret;
}

int sync_asio_tcp_client::SetKeepAlive()
{
	boost::asio::socket_base::send_buffer_size     send_size_option(csp_option.tcp_send_buffer);
	boost::asio::socket_base::receive_buffer_size  recv_size_option(csp_option.tcp_recv_buffer);
	boost::asio::ip::tcp::no_delay option(false);
	
#ifdef WIN32
	int socket_fd = -1;
	if (csp_option.type == NO_TLS)
	{
		socket_fd = raw_socket_ptr->native_handle();
 		raw_socket_ptr->set_option(send_size_option);
		raw_socket_ptr->set_option(recv_size_option);
		raw_socket_ptr->set_option(option);
	}
	else
	{
		socket_fd = sslsocket().native_handle();
		sslsocket().set_option(recv_size_option);
		sslsocket().set_option(send_size_option);
	}
	TCP_KEEPALIVE_S inKeepAlive = { 0 }; //输入参数
	unsigned long ulInLen = sizeof(TCP_KEEPALIVE_S);
	TCP_KEEPALIVE_S outKeepAlive = { 0 }; //输出参数
	unsigned long ulOutLen = sizeof(TCP_KEEPALIVE_S);
	unsigned long ulBytesReturn = 0;
	inKeepAlive.onoff = 1;
	inKeepAlive.keepaliveinterval = 10000; //两次KeepAlive探测间的时间间隔
	inKeepAlive.keepalivetime = 120000; //开始首次KeepAlive探测前的TCP空闭时间
	if (WSAIoctl((unsigned int)socket_fd, SIO_KEEPALIVE_VALS,
		(LPVOID)&inKeepAlive, ulInLen,
		(LPVOID)&outKeepAlive, ulOutLen,
		&ulBytesReturn, NULL, NULL) == SOCKET_ERROR)
	{
		int c = 0;
	}
#else
	if (csp_option.type == NO_TLS)
	{
		raw_socket_ptr->set_option(option);
		raw_socket_ptr->set_option(send_size_option);
		raw_socket_ptr->set_option(recv_size_option);
		raw_socket_ptr->set_option(boost::asio::socket_base::keep_alive(true));
		raw_socket_ptr->set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPIDLE>(120)); // 无数据交互心跳发送时间秒
		raw_socket_ptr->set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPINTVL>(10)); // 心跳包探测间隔时间
		raw_socket_ptr->set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPCNT>(5));    // 无心跳后尝试次数
		raw_socket_ptr->set_option(boost::asio::ip::tcp::socket::reuse_address(true));
	}
	else
	{
		sslsocket().set_option(recv_size_option);
		sslsocket().set_option(send_size_option);
		sslsocket().set_option(boost::asio::socket_base::keep_alive(true));
		sslsocket().set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPIDLE>(120)); // 无数据交互心跳发送时间秒
		sslsocket().set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPINTVL>(10)); // 心跳包探测间隔时间
		sslsocket().set_option(boost::asio::detail::socket_option::integer<SOL_TCP, TCP_KEEPCNT>(5));    // 无心跳后尝试次数
		sslsocket().set_option(boost::asio::ip::tcp::socket::reuse_address(true));

	}

#endif
	return 0;
}
/**
 * @brief 执行 SOCKS5 无认证协议
 *
 * 该函数用于在 SOCKS5 协议下执行无认证流程。
 *
 * @return int 返回值表示操作结果。
 *         - 成功执行返回 0。
 *         - 写入协商请求失败返回负值。
 *         - 读取协商响应失败返回 -2。
 *         - 协商响应不符合预期返回 -3。
 */
int sync_asio_tcp_client::do_socks_none_auth(){
	// 发送协商请求
    unsigned char negotiation_request[] = {
        SOCKS5_VERSION,  // SOCKS5 协议版本
        0x01,            // 支持的认证方法数量
        NO_AUTHENTICATION // 无认证
    };
	int ret = do_socks_write((char*)negotiation_request,sizeof(negotiation_request),2000);
	if (ret <0){
		return ret;
	}
	unsigned char negotiation_response[2];
	ret = do_socks_read((char*)negotiation_response,sizeof(negotiation_response),2000);
	if (ret <=0 ){
		return -2;
	}
	if ( negotiation_response[0]!=SOCKS5_VERSION || negotiation_response[1]!=NO_AUTHENTICATION){
		return -3;
	}
	return 0;
}
/**
 * @brief 执行 SOCKS5 密码认证
 *
 * 该函数执行 SOCKS5 协议的密码认证流程。
 *
 * @return 返回值表示函数执行结果：
 *         - 成功时返回 0。
 *         - 写入协商请求失败时返回 -1。
 *         - 读取协商响应失败时返回 -2。
 *         - 协商方法错误时返回 -3。
 *         - 写入用户密码认证请求失败时返回 -4。
 *         - 读取用户密码认证响应失败时返回 -5。
 *         - 用户名或密码错误时返回 -6。
 */
int sync_asio_tcp_client::do_socks_pwd_auth(){
	// 发送协商请求
    unsigned char negotiation_request[128] = {
        SOCKS5_VERSION,  // SOCKS5 协议版本
        0x02,            // 支持的认证方法数量
        0x00, 			// 无认证
		PWD_AUTHENTICATION			// 用户密码认证
    };
	int ret = do_socks_write((char*)negotiation_request,4,2000);
	if (ret <0){
		show_message(ERR,"do_socks_pwd_auth write 1 error");
		return -1;
	}
	unsigned char negotiation_response[2];
	ret = do_socks_read((char*)negotiation_response,sizeof(negotiation_response),2000);
	if (ret <=0 ){
		show_message(ERR,"do_socks_pwd_auth read 1 error");
		return -2;
	}
	if ( negotiation_response[0]!=SOCKS5_VERSION || negotiation_response[1]!=PWD_AUTHENTICATION){
		show_message(ERR,"do_socks_pwd_auth methods error");
		return -3;
	}
	//发送用户密码认证请求
	int index = 0;
	int n = 0;
	unsigned char *buf = negotiation_request;
	n = strlen(csp_option.socks_options.proxy_user);
    buf[index++] = SOCKS5_VERSION;
    buf[index++] = n;
    memcpy(buf + index, csp_option.socks_options.proxy_user, n);
    index += n;
    n = strlen(csp_option.socks_options.proxy_pass);
    buf[index++] = n;
    memcpy(buf + index, csp_option.socks_options.proxy_pass, n);
    index += n;
	ret = do_socks_write((char*)negotiation_request,index,2000);
	if (ret <0){
		show_message(ERR,"do_socks_pwd_auth write 2 error");
		return -4;
	}
	ret = do_socks_read((char*)negotiation_response,sizeof(negotiation_response),2000);
	if (ret <=0 ){
		show_message(ERR,"do_socks_pwd_auth read 2 error");
		return -5;
	}
	if ( negotiation_response[1]!=0){
		show_message(ERR,"do_socks_pwd_auth user or pwd  error");
		return -6;
	}
	return 0;
}
/**
 * @brief 发送协商请求，尝试建立连接
 *
 * 该函数根据配置信息，通过socks代理发送协商请求，并尝试建立连接。
 *
 * @return int 返回连接结果，0表示成功，其他表示失败。
 */
int sync_asio_tcp_client::do_socks_type_connect(){
	int index = 0;
	int n = 0;
	int ret = -1;
	// 发送协商请求
    unsigned char negotiation_request[128];
	unsigned char negotiation_response[128];
	unsigned char *buf = negotiation_request;
	n = strlen(csp_option.socks_options.proxy_user);
    buf[index++] = SOCKS5_VERSION;
    buf[index++] = 0x01;//CMD 0x01:TCP连接模式;0x02:BIND定模式;0x03:UDP转发模式
	buf[index++] = 0x00;//RSV 0x00
	buf[index++] = csp_option.socks_options.proxy_conn_type;//ATYP
	if (csp_option.socks_options.proxy_conn_type == IP_V4_ADDR){
		addr2dec(AF_INET , csp_option.socks_options.dest_addr,buf + index);
        index += 4;
		*((unsigned short *)(buf + index)) = htons(csp_option.socks_options.dest_port);
    	index += 2;
	}else if (csp_option.socks_options.proxy_conn_type == IP_V6_ADDR){
		addr2dec(AF_INET6 , csp_option.socks_options.dest_addr,buf + index);
        index += 16;
		*((unsigned short *)(buf + index)) = htons(csp_option.socks_options.dest_port);
    	index += 2;
	}else if (csp_option.socks_options.proxy_conn_type == DOMAIN_ADDR){
		n = strlen(csp_option.socks_options.dest_addr);
		buf[index++] = n;
		memcpy(buf+index, csp_option.socks_options.dest_addr,n);
		index += n+1;
	}else if (csp_option.socks_options.proxy_conn_type == VSM_ID){
		n = strlen(csp_option.socks_options.vsm_id);
		buf[index++] = n;
		memcpy(buf+index, csp_option.socks_options.vsm_id,n);
		index += n+1;
	}else if (csp_option.socks_options.proxy_conn_type == TENANT_ID){
		n = strlen(csp_option.socks_options.tenant_id);
		buf[index++] = n;
		memcpy(buf+index, csp_option.socks_options.tenant_id,n);
		index += n+1;
	}else{
		show_message(ERR,"do socks proxy connect type error");
		return -1;
	}
	ret = do_socks_write((char*)negotiation_request,index,2000);
	if (ret <0){
		show_message(ERR,"do_sockes_type_connect write error");
		return -2;
	}
	ret = do_socks_read((char*)negotiation_response,10,2000);
	if (ret <=0 ){
		show_message(ERR,"do_sockes_type_connect read error:[%d]",ret);
		return -3;
	}
	if ( negotiation_response[0]!=SOCKS5_VERSION || negotiation_response[1]!=0){
		show_message(ERR,"do_sockes_type_connect connect error:[%d]",negotiation_response[1]);
		return -4;
	}	
	return 0;
}
int sync_asio_tcp_client::socks_proxy_connect(){
	int ret = -1;
	if (csp_option.socks_options.proxy_auth == 0){
		ret = do_socks_none_auth();
	}else if(csp_option.socks_options.proxy_auth == 1){
		ret = do_socks_pwd_auth();
	}else{
		show_message(ERR, "socks proxy auth error");
		return ret;
	}
	if (ret == 0){
		ret = do_socks_type_connect();
		return ret;
	}else{
		show_message(ERR, "socks proxy do first handshark error:[%d]", ret);
		return ret;
	}
	return 0;
}
int sync_asio_tcp_client::do_socks_write(const char* data, const int len, int timeout){
	boost::posix_time::time_duration td;
	if (timeout == 0)
	{
		td = boost::date_time::pos_infin;
	}
	else
	{
		td = boost::posix_time::milliseconds(timeout);
	}
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	size_t length = 0;
	if (csp_option.type == NO_TLS)
	{
		if (!raw_socket_ptr.get() || !raw_socket_ptr->is_open())
		{
			//printf("rawsocket is closed\n");
			show_message(WARN, "[write]:rawsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_write(*raw_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_write,
			this,
			new_write_msg,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
			
	}
	else
	{
		if (!ssl_socket_ptr.get() || !sslsocket().is_open())
		{
			//printf("sslsocket is closed\n");
			show_message(WARN, "[write]:sslsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_write(ssl_socket_ptr->next_layer(), boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_write,
			this,
			new_write_msg,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}
	boost::system::error_code err;
	do
	{
		io_service_ptr->run_one(err);
		if (err){
			break;
		}
		//boost::this_thread::sleep(boost::posix_time::milliseconds(50));
	} while (ec == boost::asio::error::would_block);
	if (ec)
	{
		close();
		show_message(ERR, "[send] send date false,maybe server is disconnected,now close");
		return CSP_ERR;
	}
	return CSP_OK;
}
int sync_asio_tcp_client::do_socks_read(char* data, const int len, int timeout){
	boost::posix_time::time_duration td;
	if (timeout == 0)
	{
		td = boost::date_time::pos_infin;
	}
	else
	{
		td = boost::posix_time::milliseconds(timeout);
	}
	deadline_ptr->expires_from_now(td);
	boost::system::error_code ec = boost::asio::error::would_block;
	size_t length = 0;
	if (csp_option.type == NO_TLS)
	{
		if (!raw_socket_ptr.get() || !raw_socket_ptr->is_open())
		{
			show_message(WARN, "rawsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_read(*raw_socket_ptr, boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_read,
			this,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}
	else
	{
		if (!ssl_socket_ptr.get() || !sslsocket().is_open())
		{
			show_message(WARN, "[read]:sslsocket is closed");
			return CSP_DISCONNECT;
		}
		boost::asio::async_read(ssl_socket_ptr->next_layer(), boost::asio::buffer(data, len),
			boost::bind(&sync_asio_tcp_client::handle_read,
			this,
			boost::asio::placeholders::error,
			boost::asio::placeholders::bytes_transferred,
			&ec,
			&length));
	}

	boost::system::error_code err;
	do
	{
		io_service_ptr->run_one(err);
	} while (ec == boost::asio::error::would_block);
	if (ec)
	{
		//超时
		if (ec == boost::asio::error::operation_aborted)
		{
			return 0;
		}
		//连接断开
		else
		{
			close();
			show_message(ERR, "[read]:read error,maybe server is disconnect,now close");
			return CSP_DISCONNECT;
		}
	}
	return length;
}