#pragma once
/*
* Copyright (c) 2023，启明星辰
* All rights reserved.
*
* 文件名称：csp_session.h
* 文件标识：客户端数据读写接口
*
* 历史版本：1.0
* 作    者：高海清
* 初始化完成日期：2023年5月5日
* 修改日期：2025年9月29日
* 修改内容：添加加密套件和密钥交换算法组
*/
#ifdef WIN32
	#ifdef CSPDLL_EXPORTS
		#define CSPDLL_API __declspec(dllexport)
	#else
		#define CSPDLL_API 
	#endif
	#include <windows.h>

#else
	#define CSPDLL_API __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif
/********************************/
#define CSP_OK 0
#define CSP_ERR -1
#define CSP_INIT_SSL_ERR -2
#define CSP_HANDSHAKE_ERR -3
#define CSP_BUF_NOT_ENOUGH -4
#define CSP_DISCONNECT -5
#define CSP_LOAD_CERTKEY_ERR -6
#define CSP_SIGNKEY_NOT_MATCH -7
#define CSP_ENKEY_NOT_MATCH -8
#define CSP_INPUT_ARG_ERR -9
#define CSP_OPEN_P12_FILE_ERR -10
#define CSP_READ_P12_FILE_ERR -11
#define CSP_P12_PASSWD_ERR -12
/********************************/
typedef enum {
	NO_TLS = 0,//无加密传输
	STD_TLS, //标准ssl/TLS1.2传输
	GM_TLS   //国密gmtls1.1传输
}TransferType;

typedef enum {
	IP_V4_ADDR  = 0x01, //IPV4地址
	DOMAIN_ADDR = 0x03, //域名地址
	IP_V6_ADDR  = 0x04, //IPV6地址
	VSM_ID      = 0x60, //虚拟机ID
	TENANT_ID   = 0x61, //租户ID
}SocksConnType;

struct cspSession;
typedef void (cspShowMessage)(int log_level, const char* message_ptr, ...);//日志输出函数
	//配置选项
#pragma pack(push, 1)
//2024/11/4 添加socks代理配置
	typedef struct sockesOptions{
		char proxy_addr[128]; 	//socks5代理服务器地址
		int  proxy_port; 	  	//socks5代理服务器端口
		char dest_addr[128]; 	//socks5代理目标服务器地址
		int  dest_port; 	  	//socks5代理目标服务器端口
		int  proxy_auth;      	//socks5代理认证方式 0:不认证,1:用户名密码认证;
		char proxy_user[32];  	//socks5代理用户名
		char proxy_pass[32];  	//socks5代理密码
		SocksConnType  proxy_conn_type;	//socks5连接类型
		char vsm_id[32];  	  	//socks5代理目标的虚拟机ID
		char tenant_id[32];   	//socks5代理租户订购的租户ID
	}socksOptions_t;

	typedef struct cspOptions{
		TransferType type;    //连接类型
		int  connect_timeout;  //连接超时
		int  server_port;     //服务器端口
		char server_addr[128];//服务器地址支持域名
		char source_addr[128];//源地址可不用
		int  tcp_send_buffer;//tcp发送缓冲区大小
		int  tcp_recv_buffer;//tcp接收缓冲区大小
		int  use_socks_proxy;//是否使用socks5代理0:不使用,1:使用
		socksOptions_t socks_options; //socks5代理配置
		struct {
			char cafile[256];     //ca证书
			char signcertfile[256];   //签名证书
			char signkeyfile[256];    //签名私钥
			char encertfile[256];   //加密证书
			char enkeyfile[256];    //加密私钥
			char signp12[256];      //签名p12 
			char signpasswd[32];    //签名p12密码
			char encp12[256];       //加密p12
			char encpasswd[32];     //加密p12密码
			char ciphers[512];     //加密套件
			char groups[512];     //密钥交换算法组
			char load_order;        //证书加载顺序 0:先加载签名证书和私钥 1:先加载加密证书和私钥
			int verity_mode;     //校验证书模式 0:不校验服务端证书,1:校验服务端证书
		} ssl_option;
		cspShowMessage * logger_ptr;//日志输出回调函数指针
	} cspOptions;
#pragma pack(pop)
	//会话连接对象
	typedef struct cspSession {
		TransferType type;    //连接类型
		int err;              //失败错误详情
		cspOptions csp_option;//配置选项
		void *privdata;       //内部私有对象指针
		void *user_data1;   //扩展用户字段1
		void *user_data2;   //扩展用户字段2
		void(*free_privdata)(void *);
	} cspSession;

	//***************************************************************************
	//功能：获取连接对象
	//输入：cspOptions
	//输出：cspSession
	//***************************************************************************
	CSPDLL_API cspSession *cspConnectWithOptions(const cspOptions *options);

	//***************************************************************************
	//功能：读取数据
	//输入: 1:会话连接对象;2:数据缓存区;3:需读取数据的长度;4、读取超时(毫秒，为0表示不设置超时)
	//输出: 大于0读取成功，等于0未读取到数据，小于0失败(参考错误返回码)
	//***************************************************************************
	CSPDLL_API int cspRead(cspSession *c, char *pData, int len, int timeout_ms);

	//***************************************************************************
	//功能：发送数据
	//输入: 1:会话连接对象;2:数据;3:数据长度;4、写入超时(毫秒，为0表示不设置超时)
	//输出: 等于0写入成功，小于0失败(参考错误返回码)
	//***************************************************************************
	CSPDLL_API int cspSend(cspSession *c, char *pData, int len, int timeout_ms);

	//***************************************************************************
	//功能：检查会话连接状态
	//输入: 会话连接对象
	//输出: CSP_OK:连接正常，CSP_ERR连接失败
	//***************************************************************************
	CSPDLL_API int cspCheckConnection(cspSession *c);

	//***************************************************************************
	//功能：会话重连
	//输入: 会话连接对象
	//输出: CSP_OK:连接正常，CSP_ERR连接失败
	//***************************************************************************
	CSPDLL_API int cspReconnect(cspSession *c);

	//***************************************************************************
	//功能：断开连接
	//输入: 会话连接对象
	//输出: 无
	//***************************************************************************
	CSPDLL_API void cspDisconnect(cspSession *c);

	//***************************************************************************
	//功能：释放会话连接对象
	//输入: 会话连接对象
	//输出: 无
	//***************************************************************************
	CSPDLL_API void cspConnectionFree(cspSession *c);

	//连接池对象,线程安全可多线程调用
	typedef struct cspSessionPool {
		int max_sessions;     //最大连接数
		int err;              //错误码
		cspOptions csp_option;//配置选项
		void *privdata;       //内部私有对象指针
		void(*free_privdata)(void *);

	} cspSessionPool;
	//***************************************************************************
	//功能：创建会话连接池
	//输入: 配置，最大连接数
	//输出: 无
	//***************************************************************************
	CSPDLL_API cspSessionPool *cspCreateSessionPool(const cspOptions *options, int max_sessions);

	//***************************************************************************
	//功能：从连接池获取一个连接对象
	//输入: 会话连接池,获取的超时时间
	//输出: 连接对象
	//***************************************************************************
	CSPDLL_API cspSession * cspGetSessionFromPool(cspSessionPool *pc, int timeout_ms);

	//***************************************************************************
	//功能：从连接池获取的对象放回到连接池中
	//输入: 连接池,连接对象
	//输出: 无
	//***************************************************************************
	CSPDLL_API void cspBackSessionToPool(cspSessionPool *pc, cspSession *c);

	//***************************************************************************
	//功能：释放会话连接池内存
	//输入: 会话连接池
	//输出: 无
	//***************************************************************************
	CSPDLL_API void cspFreeSessionPool(cspSessionPool *pc);
	/*测试用例
	int main()
	{
	cspOptions opt;
	memset(&opt,0,sizeof(cspOptions));
	opt.type=STD_TLS;
	string host="WWW.ess123.com";
	opt.server_port = 1443;
	opt.connect_timeout=3000;
	//string certkeydir="/home/<USER>/key/gmssl_certs/certs/";
	//string cacert = certkeydir + "CA.pem";
	//cacert = certkeydir + "/home/<USER>/ngnix/Nginx-with-GmSSLv3-master/tools/cacert.pem";
	//string cacert = certkeydir + "client/demo1.sm2.trust.pem";
	//string cltsigncert = certkeydir + "client/c_sign.crt";
	//string cltsignkey = certkeydir + "client/c_signkey.pem";
	#if 0
	string certkeydir="/home/<USER>/key/gmssl_certs/certs/";
	string cacert = certkeydir + "CA.pem";
	string cltsigncert = certkeydir + "CS.pem";
	string cltsignkey = certkeydir + "CS.key";

	string cltencert = certkeydir + "CE.pem";
	string cltenkey = certkeydir + "CE.key";
	#else
	string certkeydir="/home/<USER>/key/client/";
	string cacert = certkeydir + "SMxCAforNAC.cer";
	string cltsigncert = certkeydir + "本地网关国密证书_sign.cer";
	string cltsignkey = certkeydir + "本地网关国密证书_sign.key";

	string cltencert = certkeydir + "本地网关国密证书_encry.cer";
	string cltenkey = certkeydir + "本地网关国密证书_encry.key";
	#endif
	string str_p12="client.p12";
	string str_pass="hhhhhh1";
	//memcpy(opt.ssl_option.cafile,cacert.c_str(),cacert.length());
	memcpy(opt.ssl_option.signcertfile,cltsigncert.c_str(),cltsigncert.length());
	memcpy(opt.ssl_option.signkeyfile,cltsignkey.c_str() ,cltsignkey.length());
	memcpy(opt.ssl_option.encertfile,cltencert.c_str(),cltencert.length());
	memcpy(opt.ssl_option.enkeyfile,cltenkey.c_str(),cltenkey.length());
	memcpy(opt.ssl_option.signp12,str_p12.c_str(),str_p12.length());
	memcpy(opt.ssl_option.signpasswd,str_pass.c_str(),str_pass.length());
	opt.ssl_option.verity_mode=1;
	opt.ssl_option.load_order=0;
	opt.type=NO_TLS;
	memcpy(opt.server_addr,host.c_str(),host.length());

	cspSessionPool * pc  = cspCreateSessionPool(&opt,5);

	while(true)
	{
	cspSession *connection =  cspGetSessionFromPool(pc,200);
	if(connection)
	{
	printf("connect is ok\n");
	}
	else
	{
	printf("no connection ,now continue:[%d]\n",time(NULL));
	continue;
	}
	while(true)
	{
	char pdata[1024]={0};
	int ret = cspRead(connection,pdata,5,1000);
	printf("now ret:[%d]\n",ret);
	if(ret>0)
	{
	//printf("data:[%s]\n",pdata);
	int rc = cspSend(connection,pdata,5,1000);
	//printf("rc = [%d]\n",rc);
	}
	if(ret<0)
	{
	printf("server is disconnected\n");
	cspBackSessionToPool(pc,connection);
	break;
	}

	}
	}
	cspFreeSessionPool(pc);
	return 0;
	}
	*/
#ifdef __cplusplus
}
#endif

